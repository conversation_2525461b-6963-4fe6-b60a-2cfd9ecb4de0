MVAR_ENTRIES = {
    "hasc": ("OS/2", "sTypoAscender"),  # horizontal ascender
    "hdsc": ("OS/2", "sTypoDescender"),  # horizontal descender
    "hlgp": ("OS/2", "sTypoLineGap"),  # horizontal line gap
    "hcla": ("OS/2", "usWinAscent"),  # horizontal clipping ascent
    "hcld": ("OS/2", "usWinDescent"),  # horizontal clipping descent
    "vasc": ("vhea", "ascent"),  # vertical ascender
    "vdsc": ("vhea", "descent"),  # vertical descender
    "vlgp": ("vhea", "lineGap"),  # vertical line gap
    "hcrs": ("hhea", "caretSlopeRise"),  # horizontal caret rise
    "hcrn": ("hhea", "caretSlopeRun"),  # horizontal caret run
    "hcof": ("hhea", "caretOffset"),  # horizontal caret offset
    "vcrs": ("vhea", "caretSlopeRise"),  # vertical caret rise
    "vcrn": ("vhea", "caretSlopeRun"),  # vertical caret run
    "vcof": ("vhea", "caretOffset"),  # vertical caret offset
    "xhgt": ("OS/2", "sxHeight"),  # x height
    "cpht": ("OS/2", "sCapHeight"),  # cap height
    "sbxs": ("OS/2", "ySubscriptXSize"),  # subscript em x size
    "sbys": ("OS/2", "ySubscriptYSize"),  # subscript em y size
    "sbxo": ("OS/2", "ySubscriptXOffset"),  # subscript em x offset
    "sbyo": ("OS/2", "ySubscriptYOffset"),  # subscript em y offset
    "spxs": ("OS/2", "ySuperscriptXSize"),  # superscript em x size
    "spys": ("OS/2", "ySuperscriptYSize"),  # superscript em y size
    "spxo": ("OS/2", "ySuperscriptXOffset"),  # superscript em x offset
    "spyo": ("OS/2", "ySuperscriptYOffset"),  # superscript em y offset
    "strs": ("OS/2", "yStrikeoutSize"),  # strikeout size
    "stro": ("OS/2", "yStrikeoutPosition"),  # strikeout offset
    "unds": ("post", "underlineThickness"),  # underline size
    "undo": ("post", "underlinePosition"),  # underline offset
    #'gsp0': ('gasp', 'gaspRange[0].rangeMaxPPEM'),	 # gaspRange[0]
    #'gsp1': ('gasp', 'gaspRange[1].rangeMaxPPEM'),	 # gaspRange[1]
    #'gsp2': ('gasp', 'gaspRange[2].rangeMaxPPEM'),	 # gaspRange[2]
    #'gsp3': ('gasp', 'gaspRange[3].rangeMaxPPEM'),	 # gaspRange[3]
    #'gsp4': ('gasp', 'gaspRange[4].rangeMaxPPEM'),	 # gaspRange[4]
    #'gsp5': ('gasp', 'gaspRange[5].rangeMaxPPEM'),	 # gaspRange[5]
    #'gsp6': ('gasp', 'gaspRange[6].rangeMaxPPEM'),	 # gaspRange[6]
    #'gsp7': ('gasp', 'gaspRange[7].rangeMaxPPEM'),	 # gaspRange[7]
    #'gsp8': ('gasp', 'gaspRange[8].rangeMaxPPEM'),	 # gaspRange[8]
    #'gsp9': ('gasp', 'gaspRange[9].rangeMaxPPEM'),	 # gaspRange[9]
}
